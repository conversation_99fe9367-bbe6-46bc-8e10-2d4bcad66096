<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Stories | NAROOP Mobile Prototype</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.4.1/dist/tailwind.min.css" rel="stylesheet">
    <link rel="icon" href="/src/assets/naroop-logo.svg" type="image/svg+xml">
  </head>
  <body class="bg-slate-50 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="flex items-center justify-between px-4 py-3 bg-white shadow-sm sticky top-0 z-10">
      <div class="flex items-center gap-2">
        <img src="/src/assets/naroop-logo.svg" alt="NAROOP Logo" class="h-8 w-8" />
        <span class="text-xl font-bold tracking-tight text-gray-900">Stories</span>
      </div>
      <button aria-label="Notifications" class="p-2 rounded-full hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-primary-500">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-gray-500">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>
      </button>
    </header>

    <!-- Main Content -->
    <main class="flex-1 overflow-y-auto pb-20 px-2 max-w-md mx-auto w-full">
      <!-- Create Post Section -->
      <section class="bg-white rounded-lg shadow-md p-4 mt-4 mb-6 flex items-start gap-3">
        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User avatar" class="h-10 w-10 rounded-full object-cover" />
        <form class="flex-1 flex flex-col gap-2">
          <input type="text" placeholder="What's on your mind, John?" class="w-full bg-slate-50 border border-slate-200 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 text-gray-900 placeholder-gray-400" />
          <button type="submit" class="self-end bg-primary-600 hover:bg-primary-700 text-white font-semibold px-5 py-2 rounded-lg shadow transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500">Post</button>
        </form>
      </section>

      <!-- Story Feed -->
      <section class="flex flex-col gap-4">
        <!-- Example Story Card -->
        <article class="bg-white rounded-lg shadow-md p-4 flex flex-col gap-2">
          <div class="flex items-center gap-3 mb-1">
            <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User avatar" class="h-9 w-9 rounded-full object-cover" />
            <div>
              <div class="font-semibold text-gray-900">Aisha Williams</div>
              <div class="text-xs text-gray-400">2 hours ago</div>
            </div>
          </div>
          <h2 class="font-bold text-lg text-gray-900">Finding Joy in Community</h2>
          <p class="text-gray-700">Today I joined a local event and met so many inspiring people. Our stories matter and together we uplift each other!</p>
          <div class="flex items-center justify-between mt-2 pt-2 border-t border-slate-100">
            <button class="flex items-center gap-1 text-gray-500 hover:text-primary-600 text-lg p-2 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500" aria-label="Like">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6.318 6.318a4.5 4.5 0 016.364 0l.318.318.318-.318a4.5 4.5 0 116.364 6.364L12 21.364l-7.682-7.682a4.5 4.5 0 010-6.364z" />
              </svg>
              <span class="text-base">24</span>
            </button>
            <button class="flex items-center gap-1 text-gray-500 hover:text-primary-600 text-lg p-2 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500" aria-label="Comment">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 10.5h9m-9 3h6m-6 3h3m-3-9h9a2.25 2.25 0 012.25 2.25v9A2.25 2.25 0 0116.5 21H7.5A2.25 2.25 0 015.25 18.75v-9A2.25 2.25 0 017.5 7.5z" />
              </svg>
              <span class="text-base">5</span>
            </button>
            <button class="flex items-center gap-1 text-gray-500 hover:text-primary-600 text-lg p-2 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500" aria-label="Share">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-3A2.25 2.25 0 008.25 5.25V9m7.5 0V5.25A2.25 2.25 0 0013.5 3h-3A2.25 2.25 0 008.25 5.25V9m7.5 0a2.25 2.25 0 012.25 2.25v9A2.25 2.25 0 0116.5 21H7.5A2.25 2.25 0 015.25 18.75v-9A2.25 2.25 0 017.5 7.5z" />
              </svg>
            </button>
          </div>
        </article>
        <!-- More story cards can be added here -->
      </section>
    </main>

    <!-- Bottom Navigation Bar -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-slate-200 shadow z-20">
      <div class="flex justify-between items-center max-w-md mx-auto px-2 py-1">
        <a href="#" class="flex flex-col items-center flex-1 py-2 text-primary-600" aria-label="Home">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-7 h-7 mb-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3 12l9-9 9 9M4.5 10.5V21h15V10.5" />
          </svg>
          <span class="text-xs font-medium">Home</span>
        </a>
        <a href="#" class="flex flex-col items-center flex-1 py-2 text-gray-500 hover:text-primary-600" aria-label="Stories">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-7 h-7 mb-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-10.5A2.25 2.25 0 014.5 17.25V6.75A2.25 2.25 0 016.75 4.5h10.5A2.25 2.25 0 0119.5 6.75z" />
          </svg>
          <span class="text-xs font-medium">Stories</span>
        </a>
        <a href="#" class="flex flex-col items-center flex-1 py-2 text-gray-500 hover:text-primary-600" aria-label="Connect">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-7 h-7 mb-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 6.75a4.5 4.5 0 11-9 0 4.5 4.5 0 019 0zM4.5 20.25a8.25 8.25 0 0115 0" />
          </svg>
          <span class="text-xs font-medium">Connect</span>
        </a>
        <a href="#" class="flex flex-col items-center flex-1 py-2 text-gray-500 hover:text-primary-600" aria-label="Explore">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-7 h-7 mb-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v1.5m0 15V21m8.25-9H21m-15 0H3m15.364-6.364l-1.06 1.06M6.343 17.657l-1.06 1.06m12.02 0l-1.06-1.06M6.343 6.343l-1.06-1.06" />
          </svg>
          <span class="text-xs font-medium">Explore</span>
        </a>
        <a href="#" class="flex flex-col items-center flex-1 py-2 text-gray-500 hover:text-primary-600" aria-label="Profile">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-7 h-7 mb-1">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6.75a4.5 4.5 0 11-9 0 4.5 4.5 0 019 0zM4.5 20.25a8.25 8.25 0 0115 0" />
          </svg>
          <span class="text-xs font-medium">Profile</span>
        </a>
      </div>
    </nav>
  </body>
</html>
